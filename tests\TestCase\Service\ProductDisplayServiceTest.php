<?php

namespace App\Test\TestCase\Service;

use App\Service\ProductDisplayService;
use App\Model\Entity\Product;
use App\Model\Entity\Budget;
use App\Model\Entity\RandselOrder;
use Cake\TestSuite\TestCase;
use Cake\ORM\TableRegistry;
use Cake\I18n\FrozenTime;
use Cake\Cache\Cache;

/**
 * ProductDisplayServiceTest
 */
class ProductDisplayServiceTest extends TestCase
{
    /**
     * @var array
     */
    protected $fixtures = [
        'app.Products',
        'app.Budgets',
        'app.RandselOrders',
        'app.Makers',
        'app.Brands',
        'app.GeneralUsers'
    ];

    /**
     * @var ProductDisplayService
     */
    private ProductDisplayService $service;

    /**
     * @var \App\Model\Table\ProductsTable
     */
    private $productsTable;

    /**
     * @var \App\Model\Table\BudgetsTable
     */
    private $budgetsTable;

    /**
     * @var \App\Model\Table\RandselOrdersTable
     */
    private $randselOrdersTable;

    public function setUp(): void
    {
        parent::setUp();

        $this->service = new ProductDisplayService();
        $this->productsTable = TableRegistry::getTableLocator()->get('Products');
        $this->budgetsTable = TableRegistry::getTableLocator()->get('Budgets');
        $this->randselOrdersTable = TableRegistry::getTableLocator()->get('RandselOrders');

        // テストデータをクリア
        $this->budgetsTable->deleteAll([]);
        $this->randselOrdersTable->deleteAll([]);

        // キャッシュをクリア
        Cache::clear();
    }

    public function tearDown(): void
    {
        parent::tearDown();
        Cache::clear();
    }

    /**
     * 表示可能商品一覧取得のテスト
     */
    public function testGetAvailableProducts(): void
    {
        // テストデータの準備
        $this->createTestProduct();
        $this->createTestBudget();
        
        // テスト実行
        $result = $this->service->getAvailableProducts();

        debug($result);

        // 検証
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        $product = $result[0];
        $this->assertNotNull($product->display_type);
        $this->assertContains($product->display_type, [
            ProductDisplayService::DISPLAY_TYPE_PAPER,
            ProductDisplayService::DISPLAY_TYPE_DIGITAL
        ]);
    }

    /**
     * 紙カタログ優先の表示タイプ判定テスト
     */
    public function testDetermineDisplayTypePaperPriority(): void
    {
        // テストデータの準備
        $product = $this->createTestProduct();
        
        // 紙カタログの予算（優先順位高）
        $this->createTestBudget([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER,
            'budget_quantity' => 100,
            'priority' => 2
        ]);
        
        // デジタルカタログの予算（優先順位低）
        $this->createTestBudget([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_DIGITAL,
            'budget_quantity' => 999999,
            'priority' => 1
        ]);
        
        // テスト実行
        $displayType = $this->service->determineDisplayType($product);
        
        // 検証：紙カタログが優先されること
        $this->assertEquals(ProductDisplayService::DISPLAY_TYPE_PAPER, $displayType);
    }

    /**
     * 売り切れ判定のテスト
     */
    public function testDetermineDisplayTypeSoldOut(): void
    {
        // テストデータの準備
        $product = $this->createTestProduct();
        
        // 予算上限に達した紙カタログ
        $budget = $this->createTestBudget([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER,
            'budget_quantity' => 2,
            'priority' => 2
        ]);
        
        // 予算上限分の注文を作成
        $this->createTestOrder([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER
        ]);
        $this->createTestOrder([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER
        ]);
        
        // テスト実行
        $displayType = $this->service->determineDisplayType($product);
        
        // 検証：売り切れになること
        $this->assertEquals(ProductDisplayService::DISPLAY_TYPE_SOLD_OUT, $displayType);
    }

    /**
     * デジタルカタログへのフォールバックテスト
     */
    public function testDetermineDisplayTypeDigitalFallback(): void
    {
        // テストデータの準備
        $product = $this->createTestProduct();
        
        // 売り切れの紙カタログ
        $this->createTestBudget([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER,
            'budget_quantity' => 1,
            'priority' => 2
        ]);
        
        // 利用可能なデジタルカタログ
        $this->createTestBudget([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_DIGITAL,
            'budget_quantity' => 999999,
            'priority' => 1
        ]);
        
        // 紙カタログの注文を作成（売り切れ状態）
        $this->createTestOrder([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER
        ]);
        
        // テスト実行
        $displayType = $this->service->determineDisplayType($product);
        
        // 検証：デジタルカタログにフォールバックすること
        $this->assertEquals(ProductDisplayService::DISPLAY_TYPE_DIGITAL, $displayType);
    }

    /**
     * 申し込み可能性チェックのテスト
     */
    public function testIsOrderAvailable(): void
    {
        // テストデータの準備
        $product = $this->createTestProduct();
        
        $this->createTestBudget([
            'product_id' => $product->id,
            'type' => ProductDisplayService::CATALOG_TYPE_PAPER,
            'budget_quantity' => 100,
            'priority' => 2
        ]);
        
        // テスト実行
        $isPaperAvailable = $this->service->isOrderAvailable(
            $product->id, 
            ProductDisplayService::CATALOG_TYPE_PAPER
        );
        $isDigitalAvailable = $this->service->isOrderAvailable(
            $product->id, 
            ProductDisplayService::CATALOG_TYPE_DIGITAL
        );
        
        // 検証
        $this->assertTrue($isPaperAvailable);
        $this->assertFalse($isDigitalAvailable); // デジタルの予算がないため
    }

    /**
     * キャッシュ機能のテスト
     */
    public function testCacheFunction(): void
    {
        // テストデータの準備
        $product = $this->createTestProduct();
        $this->createTestBudget(['product_id' => $product->id]);
        
        // 1回目の呼び出し
        $displayType1 = $this->service->determineDisplayType($product);
        
        // 2回目の呼び出し（キャッシュから取得されるはず）
        $displayType2 = $this->service->determineDisplayType($product);
        
        // 検証
        $this->assertEquals($displayType1, $displayType2);
        
        // キャッシュクリア
        $this->service->clearDisplayTypeCache($product->id);
        
        // 3回目の呼び出し（再計算されるはず）
        $displayType3 = $this->service->determineDisplayType($product);
        
        // 検証
        $this->assertEquals($displayType1, $displayType3);
    }

    /**
     * テスト用商品を作成
     */
    private function createTestProduct(array $data = []): Product
    {
        $defaultData = [
            'maker_id' => 1,
            'brand_id' => 1,
            'is_display' => true,
            'year' => 2025,
            'display_name' => 'テスト商品',
            'sort_order' => 1
        ];
        
        $productData = array_merge($defaultData, $data);
        $product = $this->productsTable->newEntity($productData);
        
        return $this->productsTable->save($product);
    }

    /**
     * テスト用予算を作成
     */
    private function createTestBudget(array $data = []): Budget
    {
        $defaultData = [
            'product_id' => 1,
            'type' => ProductDisplayService::CATALOG_TYPE_DIGITAL,
            'price' => 500,
            'budget_quantity' => 999999,
            'is_active' => true,
            'start_date' => FrozenTime::now()->subDays(1),
            'end_date' => FrozenTime::now()->addDays(30),
            'priority' => 1
        ];
        
        $budgetData = array_merge($defaultData, $data);
        $budget = $this->budgetsTable->newEntity($budgetData);
        
        return $this->budgetsTable->save($budget);
    }

    /**
     * テスト用注文を作成
     */
    private function createTestOrder(array $data = []): RandselOrder
    {
        $defaultData = [
            'maker_id' => 1,
            'brand_id' => 1,
            'general_user_id' => 1,
            'product_id' => 1,
            'type' => ProductDisplayService::CATALOG_TYPE_DIGITAL,
            'product_name' => 'テスト商品',
            'price' => 500,
            'status' => 1
        ];
        
        $orderData = array_merge($defaultData, $data);
        $order = $this->randselOrdersTable->newEntity($orderData);
        
        return $this->randselOrdersTable->save($order);
    }
}
