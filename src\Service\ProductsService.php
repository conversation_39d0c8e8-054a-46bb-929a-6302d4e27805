<?php

namespace App\Service;

use App\Kuroko\ApiModel\KurokoApiPrivateStatic\Products;
use App\Service\ProductDisplayService;
use BadMethodCallException;
use Cake\Datasource\EntityInterface;
use Cake\Core\Configure;
use Cake\Log\Log;

class ProductsService implements IRestService
{
    use ServiceTrait;

    /**
     * @var array
     */
    protected array $_defaultConfig = [
        'use_mysql_products' => false, // MySQLベースの商品管理を使用するかどうか
    ];

    /**
     * @var ProductDisplayService
     */
    private ProductDisplayService $productDisplayService;

    public function add(array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function edit(string $id, array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function view(string $id, array $data = []): ?EntityInterface
    {
        throw new BadMethodCallException();
    }

    public function index(array $data = []): ?array
    {
        // 設定に基づいて使用するデータソースを決定
        $useMysqlProducts = $this->getConfig('use_mysql_products')
            ?? Configure::read('App.use_mysql_products', false);

        if ($useMysqlProducts) {
            Log::info('ProductsService: MySQLベースの商品取得を使用');
            return $this->getMysqlProducts($data);
        } else {
            Log::info('ProductsService: Kurocoベースの商品取得を使用');
            return $this->getKurocoProducts($data);
        }
    }

    public function delete(string $id, array $data = []): bool
    {
        throw new BadMethodCallException();
    }

    public function initialize(): void
    {
        $this->productDisplayService = new ProductDisplayService();
    }

    /**
     * MySQLベースの商品一覧を取得
     *
     * @param array $data リクエストデータ
     * @return array 商品配列
     */
    private function getMysqlProducts(array $data = []): array
    {
        try {
            $products = $this->productDisplayService->getAvailableProducts();

            // Kuroco形式に変換
            $convertedProducts = [];
            foreach ($products as $product) {
                $convertedProducts[] = $this->convertToKurocoFormat($product);
            }

            Log::info('ProductsService: MySQL商品取得完了', [
                'product_count' => count($convertedProducts)
            ]);

            return $convertedProducts;

        } catch (\Exception $e) {
            Log::error('ProductsService: MySQL商品取得エラー', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // エラー時はKurocoにフォールバック
            Log::warning('ProductsService: Kurocoにフォールバック');
            return $this->getKurocoProducts($data);
        }
    }

    /**
     * Kurocoベースの商品一覧を取得
     *
     * @param array $data リクエストデータ
     * @return array 商品配列
     */
    private function getKurocoProducts(array $data = []): array
    {
        $products = new Products();
        return $products->getProductList();
    }

    /**
     * MySQL商品エンティティをKuroco形式に変換
     *
     * @param object $product 商品エンティティ
     * @return array Kuroco形式の商品データ
     */
    private function convertToKurocoFormat(object $product): array
    {
        return [
            'id' => $product->id,
            'display_name' => $product->display_name,
            'description_html' => $product->description_html,
            'note_html' => $product->note_html,
            'mask_image_description' => $product->mask_image_description,
            'image_url' => $product->image_url,
            'pdf_url' => $product->pdf_url,
            'price_range' => $product->price_range,
            'weight_range' => $product->weight_range,
            'material' => $product->material,
            'year' => $product->year,
            'sort_order' => $product->sort_order,
            'display_type' => $product->display_type ?? 'digital',
            'maker' => [
                'id' => $product->maker->id ?? null,
                'name' => $product->maker->name ?? null,
            ],
            'brand' => [
                'id' => $product->brand->id ?? null,
                'name' => $product->brand->name ?? null,
            ],
            // Kuroco互換性のための追加フィールド
            'is_display' => $product->is_display,
            'created' => $product->created,
            'modified' => $product->modified,
        ];
    }

    /**
     * 商品表示タイプのキャッシュをクリア
     *
     * @param int|null $productId 商品ID
     * @return void
     */
    public function clearDisplayTypeCache(?int $productId = null): void
    {
        $this->productDisplayService->clearDisplayTypeCache($productId);
    }

    /**
     * 申し込み可能性をチェック
     *
     * @param int $productId 商品ID
     * @param int $catalogType カタログタイプ
     * @return bool 申し込み可能かどうか
     */
    public function isOrderAvailable(int $productId, int $catalogType): bool
    {
        $useMysqlProducts = $this->getConfig('use_mysql_products')
            ?? Configure::read('App.use_mysql_products', false);

        if ($useMysqlProducts) {
            return $this->productDisplayService->isOrderAvailable($productId, $catalogType);
        }

        // Kurocoベースの場合は常にtrue（既存の動作を維持）
        return true;
    }
}
