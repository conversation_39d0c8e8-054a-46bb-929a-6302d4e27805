<?php
declare(strict_types=1);

namespace App\Test\TestCase\Model\Entity;

use App\Model\Entity\RandselOrder;
use Cake\TestSuite\TestCase;

/**
 * App\Model\Entity\RandselOrder Test Case
 */
class RandselOrderTest extends TestCase
{
    /**
     * Test subject
     *
     * @var \App\Model\Entity\RandselOrder
     */
    protected $RandselOrder;

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->RandselOrder = new RandselOrder();
    }

    /**
     * tearDown method
     *
     * @return void
     */
    public function tearDown(): void
    {
        unset($this->RandselOrder);
        parent::tearDown();
    }

    /**
     * Test getTypeName method
     *
     * @return void
     */
    public function testGetTypeName(): void
    {
        // 紙カタログのテスト
        $this->RandselOrder->type = 1;
        $this->assertEquals('紙', $this->RandselOrder->getTypeName());

        // デジタルカタログのテスト
        $this->RandselOrder->type = 2;
        $this->assertEquals('デジタル', $this->RandselOrder->getTypeName());

        // 不明なタイプのテスト
        $this->RandselOrder->type = 99;
        $this->assertEquals('不明', $this->RandselOrder->getTypeName());
    }

    /**
     * Test isPaperCatalog method
     *
     * @return void
     */
    public function testIsPaperCatalog(): void
    {
        // 紙カタログのテスト
        $this->RandselOrder->type = 1;
        $this->assertTrue($this->RandselOrder->isPaperCatalog());

        // デジタルカタログのテスト
        $this->RandselOrder->type = 2;
        $this->assertFalse($this->RandselOrder->isPaperCatalog());
    }

    /**
     * Test isDigitalCatalog method
     *
     * @return void
     */
    public function testIsDigitalCatalog(): void
    {
        // デジタルカタログのテスト
        $this->RandselOrder->type = 2;
        $this->assertTrue($this->RandselOrder->isDigitalCatalog());

        // 紙カタログのテスト
        $this->RandselOrder->type = 1;
        $this->assertFalse($this->RandselOrder->isDigitalCatalog());
    }
}
