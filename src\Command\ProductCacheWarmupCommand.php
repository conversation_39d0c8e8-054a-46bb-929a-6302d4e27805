<?php

namespace App\Command;

use App\Service\ProductCacheService;
use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;

/**
 * 商品キャッシュウォームアップコマンド
 * 
 * 商品表示タイプを事前計算してキャッシュに保存する
 * 
 * 使用例:
 * docker-compose exec amazonlinux2 php bin/cake product_cache_warmup
 * docker-compose exec amazonlinux2 php bin/cake product_cache_warmup --clear-first
 */
class ProductCacheWarmupCommand extends Command
{
    /**
     * @var ProductCacheService
     */
    private ProductCacheService $productCacheService;

    public function initialize(): void
    {
        parent::initialize();
        $this->productCacheService = new ProductCacheService();
    }

    /**
     * コマンドオプションの設定
     */
    protected function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser
            ->setDescription('商品表示タイプのキャッシュウォームアップを実行します')
            ->addOption('clear-first', [
                'help' => 'ウォームアップ前に既存のキャッシュをクリアします',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('stats', [
                'help' => 'キャッシュ統計情報を表示します',
                'boolean' => true,
                'default' => false,
            ])
            ->addOption('cleanup', [
                'help' => '期限切れキャッシュのクリーンアップを実行します',
                'boolean' => true,
                'default' => false,
            ]);

        return $parser;
    }

    /**
     * コマンド実行
     */
    public function execute(Arguments $args, ConsoleIo $io): int
    {
        $io->out('<info>商品キャッシュウォームアップコマンドを開始します</info>');
        $io->hr();

        try {
            // 統計情報表示
            if ($args->getOption('stats')) {
                $this->displayCacheStats($io);
                return static::CODE_SUCCESS;
            }

            // クリーンアップ実行
            if ($args->getOption('cleanup')) {
                $this->executeCleanup($io);
                return static::CODE_SUCCESS;
            }

            // 既存キャッシュのクリア
            if ($args->getOption('clear-first')) {
                $io->out('<warning>既存のキャッシュをクリアしています...</warning>');
                $this->productCacheService->clearProductCache();
                $io->out('<success>キャッシュクリア完了</success>');
                $io->hr();
            }

            // ウォームアップ実行
            $io->out('<info>商品表示タイプのウォームアップを開始します...</info>');
            
            $result = $this->productCacheService->warmupProductDisplayTypes();
            
            // 結果表示
            $this->displayWarmupResult($io, $result);
            
            // 統計情報表示
            $io->hr();
            $this->displayCacheStats($io);
            
            $io->out('<success>商品キャッシュウォームアップが完了しました</success>');
            
            return static::CODE_SUCCESS;
            
        } catch (\Exception $e) {
            $io->error('エラーが発生しました: ' . $e->getMessage());
            $io->error('スタックトレース: ' . $e->getTraceAsString());
            
            return static::CODE_ERROR;
        }
    }

    /**
     * ウォームアップ結果を表示
     */
    private function displayWarmupResult(ConsoleIo $io, array $result): void
    {
        $io->out('<info>ウォームアップ結果:</info>');
        $io->out(sprintf('  処理商品数: %d', $result['processed_count']));
        $io->out(sprintf('  エラー数: %d', $result['error_count']));
        $io->out(sprintf('  総商品数: %d', $result['total_products']));
        $io->out(sprintf('  処理時間: %s秒', $result['processing_time']));
        
        if ($result['error_count'] > 0) {
            $io->warning(sprintf('%d件のエラーが発生しました。ログを確認してください。', $result['error_count']));
        }
        
        $successRate = $result['total_products'] > 0 
            ? round(($result['processed_count'] / $result['total_products']) * 100, 2)
            : 0;
        $io->out(sprintf('  成功率: %s%%', $successRate));
    }

    /**
     * キャッシュ統計情報を表示
     */
    private function displayCacheStats(ConsoleIo $io): void
    {
        $io->out('<info>キャッシュ統計情報:</info>');
        
        $stats = $this->productCacheService->getCacheStats();
        
        $io->out(sprintf('  商品一覧キャッシュ: %s', 
            $stats['product_list_cached'] ? '<success>有効</success>' : '<warning>無効</warning>'
        ));
        $io->out(sprintf('  キャッシュ済み表示タイプ: %d', $stats['cached_display_types']));
        $io->out(sprintf('  総商品数: %d', $stats['total_products']));
        $io->out(sprintf('  キャッシュヒット率: %s%%', $stats['cache_hit_rate']));
        
        // キャッシュヒット率による推奨アクション
        if ($stats['cache_hit_rate'] < 50) {
            $io->warning('キャッシュヒット率が低いです。ウォームアップの実行を推奨します。');
        } elseif ($stats['cache_hit_rate'] >= 90) {
            $io->success('キャッシュが適切に機能しています。');
        }
    }

    /**
     * クリーンアップを実行
     */
    private function executeCleanup(ConsoleIo $io): void
    {
        $io->out('<info>期限切れキャッシュのクリーンアップを開始します...</info>');
        
        $result = $this->productCacheService->cleanupExpiredCache();
        
        $io->out('<info>クリーンアップ結果:</info>');
        $io->out(sprintf('  処理時間: %s秒', $result['processing_time']));
        $io->out(sprintf('  実行時刻: %s', $result['cleanup_time']));
        
        $io->out('<success>クリーンアップが完了しました</success>');
    }
}
