<?php
declare(strict_types=1);

namespace App\Test\Fixture;

use Cake\TestSuite\Fixture\TestFixture;
use Cake\I18n\FrozenTime;

/**
 * ProductsFixture
 */
class ProductsFixture extends TestFixture
{
    /**
     * Init method
     *
     * @return void
     */
    public function init(): void
    {
        $now = FrozenTime::now();
        
        $this->records = [
            [
                'id' => 1,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'テストランドセル1',
                'description' => 'テスト用商品1の説明',
                'catalog_image_url' => 'https://example.com/catalog1.jpg',
                'catalog_pdf_url' => 'https://example.com/catalog1.pdf',
                'price_range_min' => 50000,
                'price_range_max' => 80000,
                'weight_range_min' => 1000,
                'weight_range_max' => 1200,
                'material' => '人工皮革',
                'features' => 'テスト機能1',
                'target_gender' => 'unisex',
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
            [
                'id' => 2,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'テストランドセル2',
                'description' => 'テスト用商品2の説明',
                'catalog_image_url' => 'https://example.com/catalog2.jpg',
                'catalog_pdf_url' => 'https://example.com/catalog2.pdf',
                'price_range_min' => 60000,
                'price_range_max' => 90000,
                'weight_range_min' => 1100,
                'weight_range_max' => 1300,
                'material' => '本革',
                'features' => 'テスト機能2',
                'target_gender' => 'boys',
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
            [
                'id' => 3,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => true,
                'year' => 2026,
                'display_name' => 'テストランドセル3',
                'description' => 'テスト用商品3の説明',
                'catalog_image_url' => 'https://example.com/catalog3.jpg',
                'catalog_pdf_url' => 'https://example.com/catalog3.pdf',
                'price_range_min' => 70000,
                'price_range_max' => 100000,
                'weight_range_min' => 1200,
                'weight_range_max' => 1400,
                'material' => 'コードバン',
                'features' => 'テスト機能3',
                'target_gender' => 'girls',
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
            [
                'id' => 4,
                'maker_id' => 1,
                'brand_id' => 1,
                'is_display' => false,
                'year' => 2026,
                'display_name' => 'テストランドセル4（非表示）',
                'description' => 'テスト用商品4の説明（非表示）',
                'catalog_image_url' => 'https://example.com/catalog4.jpg',
                'catalog_pdf_url' => 'https://example.com/catalog4.pdf',
                'price_range_min' => 40000,
                'price_range_max' => 70000,
                'weight_range_min' => 900,
                'weight_range_max' => 1100,
                'material' => '人工皮革',
                'features' => 'テスト機能4',
                'target_gender' => 'unisex',
                'created' => $now->format('Y-m-d H:i:s'),
                'modified' => $now->format('Y-m-d H:i:s'),
            ],
        ];
        
        parent::init();
    }
}
