<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

/**
 * 商品表示制御のリアルタイム計算最適化のためのインデックス追加
 * 
 * 低頻度アクセスでリアルタイム性を重視する要件に対応
 */
class OptimizeProductDisplayIndexes extends AbstractMigration
{
    /**
     * インデックス追加
     */
    public function up(): void
    {
        // BUDGETSテーブルの最適化インデックス
        $budgetsTable = $this->table('budgets');
        
        // 商品ID + アクティブフラグ + 期間での検索最適化
        $budgetsTable->addIndex(
            ['product_id', 'is_active', 'start_date', 'end_date'],
            [
                'name' => 'idx_budgets_product_active_period',
                // 'comment' => '商品の有効予算検索用複合インデックス'
            ]
        );
        
        // 優先順位 + タイプでのソート最適化
        $budgetsTable->addIndex(
            ['product_id', 'priority', 'type'],
            [
                'name' => 'idx_budgets_product_priority_type',
                // 'comment' => '予算優先順位ソート用インデックス'
            ]
        );
        
        $budgetsTable->update();

        // RANDSEL_ORDERSテーブルの最適化インデックス
        $ordersTable = $this->table('randsel_orders');
        
        // 商品ID + タイプ + 作成日での注文数カウント最適化
        $ordersTable->addIndex(
            ['product_id', 'type', 'created'],
            [
                'name' => 'idx_orders_product_type_created',
                // 'comment' => '商品別注文数カウント用複合インデックス'
            ]
        );
        
        // ステータス別の検索最適化（必要に応じて）
        $ordersTable->addIndex(
            ['product_id', 'type', 'status', 'created'],
            [
                'name' => 'idx_orders_product_type_status_created',
                // 'comment' => 'ステータス別注文数カウント用インデックス'
            ]
        );
        
        $ordersTable->update();

        // PRODUCTSテーブルの最適化インデックス
        $productsTable = $this->table('products');
        
        // 表示フラグ + ソート順での商品一覧取得最適化
        $productsTable->addIndex(
            ['is_display', 'sort_order'],
            [
                'name' => 'idx_products_display_sort',
                // 'comment' => '表示商品一覧取得用インデックス'
            ]
        );
        
        // メーカー・ブランド別の検索最適化
        $productsTable->addIndex(
            ['maker_id', 'is_display', 'sort_order'],
            [
                'name' => 'idx_products_maker_display_sort',
                // 'comment' => 'メーカー別商品一覧用インデックス'
            ]
        );
        
        $productsTable->addIndex(
            ['brand_id', 'is_display', 'sort_order'],
            [
                'name' => 'idx_products_brand_display_sort',
                // 'comment' => 'ブランド別商品一覧用インデックス'
            ]
        );
        
        $productsTable->update();

        // パフォーマンス監視用のコメント追加
        $this->execute("
            ALTER TABLE budgets COMMENT = '商品予算管理テーブル - リアルタイム計算最適化済み';
            ALTER TABLE randsel_orders COMMENT = '注文履歴テーブル - 高速カウント最適化済み';
            ALTER TABLE products COMMENT = '商品マスタテーブル - 一覧取得最適化済み';
        ");
    }

    /**
     * インデックス削除（ロールバック）
     */
    public function down(): void
    {
        // BUDGETSテーブルのインデックス削除
        $budgetsTable = $this->table('budgets');
        
        if ($budgetsTable->hasIndex(['product_id', 'is_active', 'start_date', 'end_date'])) {
            $budgetsTable->removeIndex(['product_id', 'is_active', 'start_date', 'end_date']);
        }
        
        if ($budgetsTable->hasIndex(['product_id', 'priority', 'type'])) {
            $budgetsTable->removeIndex(['product_id', 'priority', 'type']);
        }
        
        $budgetsTable->update();

        // RANDSEL_ORDERSテーブルのインデックス削除
        $ordersTable = $this->table('randsel_orders');
        
        if ($ordersTable->hasIndex(['product_id', 'type', 'created'])) {
            $ordersTable->removeIndex(['product_id', 'type', 'created']);
        }
        
        if ($ordersTable->hasIndex(['product_id', 'type', 'status', 'created'])) {
            $ordersTable->removeIndex(['product_id', 'type', 'status', 'created']);
        }
        
        $ordersTable->update();

        // PRODUCTSテーブルのインデックス削除
        $productsTable = $this->table('products');
        
        if ($productsTable->hasIndex(['is_display', 'sort_order'])) {
            $productsTable->removeIndex(['is_display', 'sort_order']);
        }
        
        if ($productsTable->hasIndex(['maker_id', 'is_display', 'sort_order'])) {
            $productsTable->removeIndex(['maker_id', 'is_display', 'sort_order']);
        }
        
        if ($productsTable->hasIndex(['brand_id', 'is_display', 'sort_order'])) {
            $productsTable->removeIndex(['brand_id', 'is_display', 'sort_order']);
        }
        
        $productsTable->update();

        // コメントをリセット
        $this->execute("
            ALTER TABLE budgets COMMENT = '商品予算管理テーブル';
            ALTER TABLE randsel_orders COMMENT = '注文履歴テーブル';
            ALTER TABLE products COMMENT = '商品マスタテーブル';
        ");
    }
}
