<?php

namespace App\Service;

use App\Service\ProductDisplayService;
use App\Model\Table\ProductsTable;
use Cake\ORM\TableRegistry;
use Cake\Cache\Cache;
use Cake\Log\Log;
use Cake\I18n\FrozenTime;

/**
 * 商品キャッシュサービス
 * 
 * 商品表示タイプの事前計算とキャッシュ管理を行う
 */
class ProductCacheService implements IService
{
    use ServiceTrait;

    protected array $_defaultConfig = [];

    /**
     * @var ProductDisplayService
     */
    private ProductDisplayService $productDisplayService;

    /**
     * @var ProductsTable
     */
    private ProductsTable $productsTable;

    /**
     * キャッシュキー定数
     */
    public const CACHE_KEY_PRODUCT_LIST = 'product_list_with_display_types';
    public const CACHE_KEY_PRODUCT_DISPLAY_TYPE = 'product_display_type_%d';
    public const CACHE_KEY_BUDGET_USAGE = 'budget_usage_%d';

    /**
     * キャッシュ期間定数（秒）
     */
    public const CACHE_DURATION_PRODUCT_LIST = 300; // 5分
    public const CACHE_DURATION_DISPLAY_TYPE = 300; // 5分
    public const CACHE_DURATION_BUDGET_USAGE = 600; // 10分

    public function initialize(): void
    {
        $this->productDisplayService = new ProductDisplayService();
        $this->productsTable = TableRegistry::getTableLocator()->get('Products');
    }

    /**
     * 全商品の表示タイプを事前計算してキャッシュ
     * 
     * @return array 処理結果
     */
    public function warmupProductDisplayTypes(): array
    {
        $startTime = microtime(true);
        $processedCount = 0;
        $errorCount = 0;
        
        Log::info('ProductCacheService: 商品表示タイプのウォームアップ開始');
        
        // 表示フラグがtrueの全商品を取得
        $products = $this->productsTable
            ->find()
            ->where(['is_display' => true])
            ->select(['id', 'display_name'])
            ->toArray();
        
        foreach ($products as $product) {
            try {
                // 表示タイプを計算してキャッシュに保存
                $displayType = $this->productDisplayService->determineDisplayType($product);
                
                $cacheKey = sprintf(self::CACHE_KEY_PRODUCT_DISPLAY_TYPE, $product->id);
                Cache::write($cacheKey, $displayType, 'default');
                
                $processedCount++;
                
            } catch (\Exception $e) {
                Log::error('ProductCacheService: 商品表示タイプ計算エラー', [
                    'product_id' => $product->id,
                    'error' => $e->getMessage()
                ]);
                $errorCount++;
            }
        }
        
        $endTime = microtime(true);
        $processingTime = round($endTime - $startTime, 2);
        
        $result = [
            'processed_count' => $processedCount,
            'error_count' => $errorCount,
            'processing_time' => $processingTime,
            'total_products' => count($products)
        ];
        
        Log::info('ProductCacheService: 商品表示タイプのウォームアップ完了', $result);
        
        return $result;
    }

    /**
     * 商品一覧をキャッシュから取得（キャッシュがない場合は生成）
     * 
     * @return array 商品一覧
     */
    public function getCachedProductList(): array
    {
        $cacheKey = self::CACHE_KEY_PRODUCT_LIST;
        
        // キャッシュから取得を試行
        $cachedProducts = Cache::read($cacheKey, 'default');
        if ($cachedProducts !== false) {
            Log::debug('ProductCacheService: キャッシュから商品一覧を取得');
            return $cachedProducts;
        }
        
        // キャッシュがない場合は生成
        Log::debug('ProductCacheService: 商品一覧を新規生成');
        $products = $this->productDisplayService->getAvailableProducts();
        
        // キャッシュに保存
        Cache::write($cacheKey, $products, 'default');
        
        return $products;
    }

    /**
     * 指定商品の表示タイプをキャッシュから取得
     * 
     * @param int $productId 商品ID
     * @return string|null 表示タイプ（キャッシュにない場合はnull）
     */
    public function getCachedDisplayType(int $productId): ?string
    {
        $cacheKey = sprintf(self::CACHE_KEY_PRODUCT_DISPLAY_TYPE, $productId);
        $displayType = Cache::read($cacheKey, 'default');

        return $displayType !== false ? $displayType : null;
    }

    /**
     * 商品関連のキャッシュをクリア
     * 
     * @param int|null $productId 商品ID（nullの場合は全商品）
     * @return void
     */
    public function clearProductCache(?int $productId = null): void
    {
        if ($productId !== null) {
            // 指定商品のキャッシュをクリア
            $displayTypeCacheKey = sprintf(self::CACHE_KEY_PRODUCT_DISPLAY_TYPE, $productId);
            $budgetUsageCacheKey = sprintf(self::CACHE_KEY_BUDGET_USAGE, $productId);
            
            Cache::delete($displayTypeCacheKey, 'default');
            Cache::delete($budgetUsageCacheKey, 'default');
            
            Log::info('ProductCacheService: 商品キャッシュをクリア', [
                'product_id' => $productId
            ]);
        } else {
            // 全商品関連のキャッシュをクリア
            Cache::delete(self::CACHE_KEY_PRODUCT_LIST, 'default');
            
            // 個別商品のキャッシュもクリア
            $products = $this->productsTable
                ->find()
                ->select(['id'])
                ->toArray();
                
            foreach ($products as $product) {
                $this->clearProductCache($product->id);
            }
            
            Log::info('ProductCacheService: 全商品キャッシュをクリア');
        }
    }

    /**
     * 注文作成時のキャッシュ無効化
     * 
     * @param int $productId 商品ID
     * @param int $catalogType カタログタイプ
     * @return void
     */
    public function invalidateCacheOnOrder(int $productId, int $catalogType): void
    {
        // 該当商品のキャッシュをクリア
        $this->clearProductCache($productId);
        
        // 商品一覧のキャッシュもクリア（表示タイプが変わる可能性があるため）
        Cache::delete(self::CACHE_KEY_PRODUCT_LIST, 'default');
        
        Log::info('ProductCacheService: 注文作成によるキャッシュ無効化', [
            'product_id' => $productId,
            'catalog_type' => $catalogType
        ]);
    }

    /**
     * 予算更新時のキャッシュ無効化
     * 
     * @param int $productId 商品ID
     * @return void
     */
    public function invalidateCacheOnBudgetUpdate(int $productId): void
    {
        // 該当商品のキャッシュをクリア
        $this->clearProductCache($productId);
        
        // 商品一覧のキャッシュもクリア
        Cache::delete(self::CACHE_KEY_PRODUCT_LIST, 'default');
        
        Log::info('ProductCacheService: 予算更新によるキャッシュ無効化', [
            'product_id' => $productId
        ]);
    }

    /**
     * キャッシュ統計情報を取得
     * 
     * @return array キャッシュ統計
     */
    public function getCacheStats(): array
    {
        $stats = [
            'product_list_cached' => Cache::read(self::CACHE_KEY_PRODUCT_LIST, 'default') !== false,
            'cached_display_types' => 0,
            'total_products' => 0
        ];
        
        // 全商品数を取得
        $stats['total_products'] = $this->productsTable
            ->find()
            ->where(['is_display' => true])
            ->count();
        
        // キャッシュされている表示タイプの数をカウント
        $products = $this->productsTable
            ->find()
            ->where(['is_display' => true])
            ->select(['id'])
            ->toArray();
            
        foreach ($products as $product) {
            $cacheKey = sprintf(self::CACHE_KEY_PRODUCT_DISPLAY_TYPE, $product->id);
            if (Cache::read($cacheKey, 'default') !== false) {
                $stats['cached_display_types']++;
            }
        }
        
        $stats['cache_hit_rate'] = $stats['total_products'] > 0 
            ? round(($stats['cached_display_types'] / $stats['total_products']) * 100, 2)
            : 0;
        
        return $stats;
    }

    /**
     * 期限切れキャッシュのクリーンアップ
     * 
     * @return array クリーンアップ結果
     */
    public function cleanupExpiredCache(): array
    {
        $startTime = microtime(true);
        
        Log::info('ProductCacheService: 期限切れキャッシュのクリーンアップ開始');
        
        // CakePHPのキャッシュエンジンによる自動クリーンアップに依存
        // 必要に応じて手動でのクリーンアップロジックを実装
        
        $endTime = microtime(true);
        $processingTime = round($endTime - $startTime, 2);
        
        $result = [
            'processing_time' => $processingTime,
            'cleanup_time' => FrozenTime::now()->toISOString()
        ];
        
        Log::info('ProductCacheService: 期限切れキャッシュのクリーンアップ完了', $result);
        
        return $result;
    }
}
