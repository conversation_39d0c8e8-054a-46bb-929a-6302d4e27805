<?php
declare(strict_types=1);

use Migrations\AbstractMigration;

class AddTypeToRandselOrders extends AbstractMigration
{
    /**
     * Up Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-up-method
     * @return void
     */
    public function up(): void
    {
        $this->table('randsel_orders')
            ->addColumn('type', 'integer', [
                'comment' => 'カタログタイプ(1:紙, 2:デジタル)',
                'default' => 1,
                'limit' => null,
                'null' => false,
                'after' => 'product_id',
            ])
            ->addIndex(
                [
                    'type',
                ],
                [
                    'name' => 'idx_randsel_orders_type',
                ]
            )
            ->addIndex(
                [
                    'product_id',
                    'type',
                ],
                [
                    'name' => 'idx_randsel_orders_product_type',
                ]
            )
            ->addIndex(
                [
                    'product_id',
                    'type',
                    'created',
                ],
                [
                    'name' => 'idx_randsel_orders_product_type_created',
                ]
            )
            ->update();
    }

    /**
     * Down Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-down-method
     * @return void
     */
    public function down(): void
    {
        $this->table('randsel_orders')
            ->removeIndex(['type'])
            ->removeIndex(['product_id', 'type'])
            ->removeIndex(['product_id', 'type', 'created'])
            ->removeColumn('type')
            ->update();
    }
}
